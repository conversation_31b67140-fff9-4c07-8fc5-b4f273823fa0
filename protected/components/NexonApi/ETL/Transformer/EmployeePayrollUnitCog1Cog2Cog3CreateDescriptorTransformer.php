<?php

declare(strict_types=1);

namespace Components\NexonApi\ETL\Transformer;

use Components\Core\Enum\AppSettingsIdEnum;
use Components\NexonApi\Guesser\EmployeeDescriptorToValidityRowGuesser;
use Components\NexonApi\Guesser\EmployeeValidityToDescriptorGuesser;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use Components\NexonApi\Enum\EmployeeTablesFieldsEnum;
use Components\NexonApi\Enum\NexonFieldsEnum;
use Components\Core\Descriptor\ValidityRowDescriptor;
use Components\Core\Transformer\ValidityRowDescriptorSortingToArrayTransformer;

final readonly class EmployeePayrollUnitCog1Cog2Cog3CreateDescriptorTransformer implements Transformer
{
    public function __construct(
        private string $arrayEntryName
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $identifierMappingRows = [];
        if ($context->cache()->has(AppSettingsIdEnum::IDENTIFIER_MAPPING)) {
            $identifierMappingRows = iterator_to_array($context->cache()->read(AppSettingsIdEnum::IDENTIFIER_MAPPING));
        }

        $payrollRows = iterator_to_array($context->cache()->read('payrolls'));
        $unitRows = iterator_to_array($context->cache()->read('units'));
        $companyOrgGroup1Rows = iterator_to_array($context->cache()->read('companyOrgGroup1'));
        $companyOrgGroup2Rows = iterator_to_array($context->cache()->read('companyOrgGroup2'));
        $companyOrgGroup3Rows = iterator_to_array($context->cache()->read('companyOrgGroup3'));

        $transformer = function (Row $row) use ($identifierMappingRows, $payrollRows, $unitRows, $companyOrgGroup1Rows,
            $companyOrgGroup2Rows, $companyOrgGroup3Rows): Row {
            $descriptors = [];

            $identifierMappingArray = $this->rowsToArray($identifierMappingRows);
            $payrollArray = $this->rowsToArray($payrollRows);
            $unitArray = $this->rowsToArray($unitRows);
            $companyOrgGroup1Array = $this->rowsToArray($companyOrgGroup1Rows);
            $companyOrgGroup2Array = $this->rowsToArray($companyOrgGroup2Rows);
            $companyOrgGroup3Array = $this->rowsToArray($companyOrgGroup3Rows);
            $employeeDataMd5Hashs = $this->getEmployeeDescriptorDataToHash($row->valueOf('employeeFromDbDescriptors'));

            $hrRelationsShipIds = $row->valueOf('hrRelationshipIdsToPerson');
            $employeeContractNumber = 0;
            foreach ($hrRelationsShipIds as $hrRelationshipId) {
                $positionFulFillments = $row->valueOf('positionFulfillmentsToPerson');

                if (!isset($positionFulFillments[$hrRelationshipId])) {
                    continue;
                }

                $employeeContractNumber++;
                $validityRows = [];

                $employeeFromDb = $this->getEmployeeFromDb($row->valueOf('employeeFromDbDescriptors'), $row->valueOf('employeeContractFromDbDescriptors'), $employeeContractNumber);
                array_push($validityRows, ...$employeeFromDb);

                $employeeValidityByHrRelationShips = $this->setValidityByHrRelationShips($row->valueOf('hrRelationshipsToPerson'));
                array_push($validityRows, ...$employeeValidityByHrRelationShips);

                $employeeIdValidity = $this->setValidityEmployeeId($row->valueOf('employeeFromDbDescriptors'), $row->valueOf('EmployeeNumber'));
                array_push($validityRows, ...$employeeIdValidity);

                $levelSettings = [
                        2 => ['data' => $payrollArray, 'employeeTablesFieldsEnum' => EmployeeTablesFieldsEnum::PAYROLL_ID],
                        3 => ['data' => $unitArray, 'employeeTablesFieldsEnum' => EmployeeTablesFieldsEnum::UNIT_ID],
                        4 => ['data' => $companyOrgGroup1Array, 'employeeTablesFieldsEnum' => EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP1_ID],
                        5 => ['data' => $companyOrgGroup2Array, 'employeeTablesFieldsEnum' => EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP2_ID],
                        6 => ['data' => $companyOrgGroup3Array, 'employeeTablesFieldsEnum' => EmployeeTablesFieldsEnum::COMPANY_ORG_GROUP3_ID]
                ];

                $payroll = $this->setValidityPayrollUnitCog1Cog2Cog3($row->valueOf('positions'), $row->valueOf('positionFulfillmentsToPerson'), (string)$hrRelationshipId, $levelSettings);
                array_push($validityRows, ...$payroll);

                $namesValidity = $this->setValidityNames($row->valueOf('names'), $payroll);
                array_push($validityRows, ...$namesValidity);

                $employeeIdentifierValidity = $this->setEmpIdByIdentifierValidity($row, $identifierMappingArray);
                array_push($validityRows, ...$employeeIdentifierValidity);

                $taxNumberValidity = $this->setValidityTaxnumber($row->valueOf('TaxNumber'));
                array_push($validityRows, ...$taxNumberValidity);

                $validityArray = new ValidityRowDescriptorSortingToArrayTransformer();
                $employeeValidity = $validityArray->transform($validityRows);

                foreach ($employeeValidity as $key => $value) {
                    $descriptor = (new EmployeeValidityToDescriptorGuesser())->guess($value);

                    if (!in_array($descriptor->getEmployeeDataMD5Hash(), $employeeDataMd5Hashs)) {
                        $descriptors[] = $descriptor;
                    }
                }
            }
            return $row->set(new Row\Entry\ArrayEntry(
                $this->arrayEntryName,
                $descriptors
            ));
        };

        return $rows->map($transformer);
    }

    public function __serialize(): array
    {
        return [
            'arrayEntryName' => $this->arrayEntryName
        ];
    }

    public function __unserialize(array $data): void
    {
        $this->arrayEntryName = $data['arrayEntryName'];
    }

    private function getEmployeeDescriptorDataToHash($descriptorRows): array
    {
        $employeeDataMd5Hashs = [];
        foreach ($descriptorRows as $descriptorRow) {
            $employeeDataMd5Hashs[] = $descriptorRow->getEmployeeDataMD5Hash();
        }

        return $employeeDataMd5Hashs;
    }

    private function setValidityNames(array $names, array $companyUnitPayrollValidity): array
    {
        $validityRows = [];
        $validFromAndValidTo = [];
        foreach ($companyUnitPayrollValidity as $row) {
            if ($row->getValidFrom() == null) {
                continue;
            }
            $validFromAndValidTo[] = $row->getValidFrom();
            $validFromAndValidTo[] = $row->getValidTo();
        }

        $minValidFrom = empty($validFromAndValidTo) ? null : min($validFromAndValidTo);
        $maxValidTo = empty($validFromAndValidTo) ? null : max($validFromAndValidTo);

        foreach ($names as $namesKey => $namesValue) {

            $minValidFrom = $minValidFrom == null ? new \Datetime('1915-01-01') : $minValidFrom;
            $maxValidTo = $maxValidTo == null ? new \Datetime('2038-01-01') : $maxValidTo;
            $validFrom = $namesValue[NexonFieldsEnum::VALID_FROM] == null ? new \Datetime('1915-01-01') : new \Datetime($namesValue[NexonFieldsEnum::VALID_FROM]);
            $validTo = $namesValue[NexonFieldsEnum::VALID_TO] == null ? new \Datetime('2038-01-01') : new \Datetime($namesValue[NexonFieldsEnum::VALID_TO]);

            if ($validFrom > $maxValidTo || $validTo < $minValidFrom) {
                continue;
            }

            if ($namesValue[NexonFieldsEnum::VALID_FROM] == null || $validFrom < $minValidFrom) {
                $namesValue[NexonFieldsEnum::VALID_FROM] = $minValidFrom->format("Y-m-d");
            }
            if ($namesValue[NexonFieldsEnum::VALID_TO] == null || $validTo > $maxValidTo) {
                $namesValue[NexonFieldsEnum::VALID_TO] = $maxValidTo->format("Y-m-d");
            }

            $validityRow = new ValidityRowDescriptor();
            $validityRow->setValidFrom($namesValue[NexonFieldsEnum::VALID_FROM]);
            $validityRow->setValidTo($namesValue[NexonFieldsEnum::VALID_TO]);
            $tempData = [];
            $tempData[EmployeeTablesFieldsEnum::FIRST_NAME] = $namesValue[NexonFieldsEnum::FORENAME] .
                (!empty($namesValue[NexonFieldsEnum::DISTINCTIVE_TAG]) ? ' ' . $namesValue[NexonFieldsEnum::DISTINCTIVE_TAG] : '');
            $tempData[EmployeeTablesFieldsEnum::LAST_NAME] = $namesValue[NexonFieldsEnum::FAMILYNAME];
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;
        }
        return $validityRows;
    }

    private function setValidityEmployeeId(array $descriptorRows, string $employeeNumber): array
    {
        if (!empty($descriptorRows)) {
            $employeeNumber = $descriptorRows[0]->getEmployeeId();
        }
        $validityRows = [];
        $validityRow = new ValidityRowDescriptor();
        $tempData[EmployeeTablesFieldsEnum::EMPLOYEE_ID] = $employeeNumber;
        $validityRow->setValidityRowArray($tempData);
        $validityRows[] = $validityRow;
        return $validityRows;
    }

    private function setEmpIdByIdentifierValidity(Row $row, array $identifierMapping): array
    {
        $identifier = $row->valueOf('EmployeeNumber');
        if (isset($identifierMapping[EmployeeTablesFieldsEnum::EMP_ID]) && $row->has($identifierMapping[EmployeeTablesFieldsEnum::EMP_ID])) {
            $identifier = $row->valueOf($identifierMapping[EmployeeTablesFieldsEnum::EMP_ID]);
        }
        $validityRows = [];
        $validityRow = new ValidityRowDescriptor();
        $tempData = [];
        $tempData[EmployeeTablesFieldsEnum::EMP_ID] = (string)$identifier;
        $validityRow->setValidityRowArray($tempData);
        $validityRows[] = $validityRow;
        return $validityRows;
    }

    private function setValidityTaxnumber($taxNumber): array
    {
        $validityRows = [];
        $validityRow = new ValidityRowDescriptor();
        $tempData = [];
        $tempData[EmployeeTablesFieldsEnum::TAX_NUMBER] = $taxNumber;
        $validityRow->setValidityRowArray($tempData);
        $validityRows[] = $validityRow;
        return $validityRows;
    }

    private function setValidityPayrollUnitCog1Cog2Cog3(array $positions, array $positionFulFillments, string $hrRelationShipId, array $levelSettings): array
    {
        $validityRows = [];

        foreach ($positionFulFillments[$hrRelationShipId] as $positionFulFillment) {
            $positionData = $positions[$hrRelationShipId][$positionFulFillment[NexonFieldsEnum::POSITION_ID]][NexonFieldsEnum::LEVEL] ?? [];
            if (empty($positionData)) {
                continue;
            }
            foreach ($levelSettings as $levelKey => $levelSetting)
            {
                if (!isset($positionData[$levelKey])) {
                    continue;
                }
                $validityRow = new ValidityRowDescriptor();
                $tempData = [];

                $filteredDataByName = array_filter($levelSetting['data'], function($item) {
                    return isset($item[LoadGetOrgUnit3ToGroupTransformer::NAME]);
                });

                $names = array_column($filteredDataByName, LoadGetOrgUnit3ToGroupTransformer::NAME);
                $names = array_combine(array_keys($filteredDataByName), $names);
                
                $tempData[$levelSetting['employeeTablesFieldsEnum']] = array_search(
                    $positionData[$levelKey][NexonFieldsEnum::NAME][0][NexonFieldsEnum::VALUE],
                    $names
                );
                $validityRow->setValidFrom($positionFulFillment[NexonFieldsEnum::VALID_FROM]);
                $validityRow->setValidTo($positionFulFillment[NexonFieldsEnum::VALID_TO]);
                $validityRow->setValidityRowArray($tempData);
                $validityRows[] = $validityRow;
            }

            $validityRow = new ValidityRowDescriptor();
            $tempData = [];
            $tempData[EmployeeTablesFieldsEnum::COMPANY_ID] = 1;
            $validityRow->setValidFrom(null);
            $validityRow->setValidTo(null);
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;

        }
        return $validityRows;
    }


    private function getEmployeeFromDb(array $employeeDescriptorRows, array $employeeContractDescriptorRows, int $employeeContractNumber): array
    {
        $validityRows = [];
        if (empty($employeeContractDescriptorRows)) {
            return $validityRows;
        }
        $validFromAndValidTo = [];
        foreach ($employeeContractDescriptorRows as $descriptorRows) {
            foreach ($descriptorRows as $descriptorRow) {
                if ($descriptorRow->getEmployeeContractNumber() != $employeeContractNumber) {
                    continue;
                }
                $validFromAndValidTo[] = $descriptorRow->getEcValidFrom();
                $validFromAndValidTo[] = $descriptorRow->getEcValidTo();
            }
        }

        if (empty($validFromAndValidTo)) {
            return $validityRows;
        }

        $minValidFrom = min($validFromAndValidTo);
        $maxValidTo = max($validFromAndValidTo);

        foreach ($employeeDescriptorRows as $descriptorRow) {

            if (!($descriptorRow->getValidFrom() >= $minValidFrom && $descriptorRow->getValidTo() <= $maxValidTo)) {
                continue;
            }
            $validityRows[] = (new EmployeeDescriptorToValidityRowGuesser())->guess($descriptorRow);
        }

        return $validityRows;
    }

    private function setValidityByHrRelationShips(array $hrRelationshipsToPerson): array
    {
        $validityRows = [];
        foreach ($hrRelationshipsToPerson as $hrRelationship) {
            $validityRow = new ValidityRowDescriptor();
            $validityRow->setValidFrom($hrRelationship[NexonFieldsEnum::VALID_FROM]);
            $validityRow->setValidTo($hrRelationship[NexonFieldsEnum::VALID_TO]);
            $tempData = [];
            $validityRow->setValidityRowArray($tempData);

            $validityRows[] = $validityRow;
        }
        return $validityRows;
    }

    private function rowsToArray($rows)
    {
        $result = [];
        foreach ($rows as $row) {
            $result = $row->toArray();
        }
        return $result[0];
    }
}
